"""主窗口"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, 
                               QFrame, QPushButton, QLabel, QStackedWidget, 
                               QSpacerItem, QSizePolicy)
from PySide6.QtCore import QSize, Qt
from PySide6.QtGui import QIcon

from ui.components.nav_item import NavItem
from ui.pages.bookshelf_page import BookshelfPage
from ui.pages.help_page import HelpPage
from ui.pages.settings_page import SettingsPage
from ui.utils.animations import AnimationUtils
from core.models.ebook import EBook
from config.settings import WINDOW_CONFIG, NAVBAR_CONFIG, COLORS

import resources_rc


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self._setup_ui()
        self._connect_signals()
        self._setup_window()
    
    def _setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle(WINDOW_CONFIG['title'])
        self.resize(WINDOW_CONFIG['width'], WINDOW_CONFIG['height'])
        self.setMinimumSize(WINDOW_CONFIG['min_width'], WINDOW_CONFIG['min_height'])
    
    def _setup_ui(self):
        """设置UI"""
        # 主容器
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(self.central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 左侧导航栏
        self.navbar = self._create_navbar()
        
        # 右侧内容区域
        self.content_area = self._create_content_area()
        
        main_layout.addWidget(self.navbar)
        main_layout.addWidget(self.content_area)
    
    def _create_navbar(self) -> QFrame:
        """创建导航栏"""
        navbar = QFrame()
        navbar.setObjectName("navbar")
        navbar.setMinimumSize(QSize(NAVBAR_CONFIG['collapsed_width'], 0))
        navbar.setMaximumSize(QSize(NAVBAR_CONFIG['collapsed_width'], 16777215))
        navbar.setStyleSheet(f"background-color: {COLORS['primary_bg']};")
        
        navbar_layout = QVBoxLayout(navbar)
        navbar_layout.setContentsMargins(0, 0, 0, 0)
        navbar_layout.setSpacing(0)
        
        # 顶部导航区域
        top_nav = QFrame()
        top_nav_layout = QVBoxLayout(top_nav)
        top_nav_layout.setContentsMargins(0, 0, 0, 0)
        
        # 切换按钮
        self.toggle_button = QPushButton()
        self.toggle_button.setMinimumSize(QSize(NAVBAR_CONFIG['collapsed_width'], 50))
        # 使用深色主题的menu图标
        menu_icon = QIcon(":/resources/icons/dark/menu.png")
        self.toggle_button.setIcon(menu_icon)
        self.toggle_button.setIconSize(QSize(24, 24))
        self.toggle_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['primary_bg']};
                border: none;
                color: {COLORS['text_primary']};
            }}
            QPushButton:hover {{
                background-color: {COLORS['secondary_bg']};
            }}
        """)

        # 导航项
        self.nav_home = NavItem("", icon_path=":/resources/icons/dark/home.png")
        self.nav_bookshelf = NavItem("", icon_path=":/resources/icons/dark/book.png")
        
        top_nav_layout.addWidget(self.toggle_button)
        top_nav_layout.addWidget(self.nav_home)
        top_nav_layout.addWidget(self.nav_bookshelf)

        # 底部导航区域
        bottom_nav = QFrame()
        bottom_nav_layout = QVBoxLayout(bottom_nav)
        bottom_nav_layout.setContentsMargins(0, 0, 0, 0)

        # 设置导航项
        self.nav_settings = NavItem("", icon_path=":/resources/icons/dark/settings.png")
        self.nav_help = NavItem("", icon_path=":/resources/icons/dark/help.png")
        bottom_nav_layout.addWidget(self.nav_help)
        bottom_nav_layout.addWidget(self.nav_settings)

        navbar_layout.addWidget(top_nav)
        navbar_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        navbar_layout.addWidget(bottom_nav)
        
        return navbar
    
    def _create_content_area(self) -> QFrame:
        """创建内容区域"""
        content_area = QFrame()
        content_area.setStyleSheet(f"background-color: {COLORS['primary_bg']};")
        
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # 顶部栏
        self.top_bar = self._create_top_bar()

        # 页面堆栈
        self.stacked_widget = QStackedWidget()
        self._setup_pages()

        content_layout.addWidget(self.top_bar)
        content_layout.addWidget(self.stacked_widget)
        
        return content_area
    
    def _create_top_bar(self) -> QFrame:
        """创建顶部栏"""
        top_bar = QFrame()
        top_bar.setMinimumSize(QSize(0, 50))
        top_bar.setMaximumSize(QSize(16777215, 50))
        top_bar.setStyleSheet(f"background-color: {COLORS['primary_bg']};")
        
        top_bar_layout = QHBoxLayout(top_bar)
        top_bar_layout.setContentsMargins(10, 0, 10, 0)
        
        self.title_label = QLabel("哈基米")
        self.title_label.setStyleSheet(f"font-size: 14px; color: {COLORS['text_primary']};")
        
        top_bar_layout.addWidget(self.title_label)
        top_bar_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        
        return top_bar
    
    def _setup_pages(self):
        """设置页面"""
        # 默认页面
        default_page = QWidget()
        default_layout = QVBoxLayout(default_page)
        default_label = QLabel("Every One Cat")
        default_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        default_label.setStyleSheet(f"color: {COLORS['text_primary']}; font-size: 24px;")
        default_layout.addWidget(default_label)

        # 书架页面
        self.bookshelf_page = BookshelfPage()

        # 帮助页面
        self.help_page = HelpPage()

        # 设置页面
        self.settings_page = SettingsPage()

        self.stacked_widget.addWidget(default_page)  # index 0
        self.stacked_widget.addWidget(self.bookshelf_page)  # index 1
        self.stacked_widget.addWidget(self.help_page)  # index 2
        self.stacked_widget.addWidget(self.settings_page)  # index 3

        self.stacked_widget.setCurrentIndex(0)
    
    def _connect_signals(self):
        """连接信号"""
        # 这个方法会在_setup_ui之后调用，所以组件已经创建
        pass
    
    def connect_signals_after_setup(self):
        """在UI设置完成后连接信号"""
        self.toggle_button.clicked.connect(self._toggle_navbar)
        self.nav_home.clicked.connect(self._show_home_page)
        self.nav_bookshelf.clicked.connect(self._show_bookshelf_page)
        self.nav_help.clicked.connect(self._show_help_page)
        self.nav_settings.clicked.connect(self._show_settings_page)
        self.bookshelf_page.book_selected.connect(self._on_book_selected)
    
    def _toggle_navbar(self):
        """切换导航栏"""
        animation = AnimationUtils.toggle_navbar_width(self.navbar)
        animation.start()
        print("导航栏切换")
    
    def _show_home_page(self):
        """显示主页"""
        self.stacked_widget.setCurrentIndex(0)
        self._update_nav_states(home=True)
        print("显示主页")

    def _show_bookshelf_page(self):
        """显示书架页面"""
        self.stacked_widget.setCurrentIndex(1)
        self._update_nav_states(bookshelf=True)
        print("显示书架")

    def _show_help_page(self):
        """显示帮助页面"""
        self.stacked_widget.setCurrentIndex(2)
        self._update_nav_states(help=True)
        print("显示帮助")

    def _show_settings_page(self):
        """显示设置页面"""
        self.stacked_widget.setCurrentIndex(3)
        self._update_nav_states(settings=True)
        print("显示设置")

    def _update_nav_states(self, home=False, bookshelf=False, help=False, settings=False):
        """更新导航状态"""
        self.nav_home.set_active(home)
        self.nav_bookshelf.set_active(bookshelf)
        self.nav_help.set_active(help)
        self.nav_settings.set_active(settings)
    
    def _on_book_selected(self, book: EBook):
        """书籍选中事件"""
        # print(f"主窗口收到书籍选中信号: {book.title}")
        # 这里可以添加打开书籍的逻辑
        pass


