"""书籍组件"""

from PySide6.QtWidgets import <PERSON>Widget, QVBoxLayout, QLabel, QFrame, QPushButton
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QPixmap, QColor, QPainter, QFont

from core.models.ebook import <PERSON>Book
from config.settings import BOOKSHELF_CONFIG
from config.styles import BOOK_COVER_STYLE, BOOK_TITLE_STYLE, BOOK_AUTHOR_STYLE


class BookWidget(QWidget):
    """书籍显示组件"""

    # 信号：书籍被点击
    book_clicked = Signal(EBook)
    # 信号：删除按钮被点击
    delete_clicked = Signal(EBook)

    def __init__(self, book: EBook, parent=None, show_delete_button=False):
        super().__init__(parent)
        self.book = book
        self.show_delete_button = show_delete_button
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        self.setFixedSize(
            BOOKSHELF_CONFIG['book_width'], 
            BOOKSHELF_CONFIG['book_height']
        )
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 封面
        self.cover_label = QLabel()
        self.cover_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cover_label.setFixedSize(
            BOOKSHELF_CONFIG['cover_width'], 
            BOOKSHELF_CONFIG['cover_height']
        )
        self.cover_label.setFrameShape(QFrame.Shape.Box)
        self.cover_label.setStyleSheet(BOOK_COVER_STYLE)
        
        # 标题
        self.title_label = QLabel(self.book.display_title)
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title_label.setStyleSheet(BOOK_TITLE_STYLE)
        self.title_label.setWordWrap(True)
        
        # 作者
        self.add_time_label = QLabel(self.book.add_date)
        self.add_time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.add_time_label.setStyleSheet(BOOK_AUTHOR_STYLE)
        
        # 删除按钮（如果需要显示）
        if self.show_delete_button:
            self.delete_button = QPushButton("×")
            # 设置为正方形按钮，确保完全显示
            button_size = 24
            self.delete_button.setFixedSize(button_size, button_size)
            self.delete_button.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            self.delete_button.clicked.connect(self._on_delete_clicked)

            # 将删除按钮放在右上角，确保完全在组件内部
            self.delete_button.setParent(self)
            self.delete_button.move(
                BOOKSHELF_CONFIG['book_width'] - button_size - 2, 2
            )
            self.delete_button.show()

        # 加载封面
        self._load_cover()

        layout.addWidget(self.cover_label)
        layout.addWidget(self.title_label)
        layout.addWidget(self.add_time_label)
    
    def _load_cover(self):
        """加载封面图片"""
        # 简化版本：直接使用默认封面
        self._set_default_cover()
    
    def _load_cover_from_path(self, path: str):
        """从路径加载封面"""
        pixmap = QPixmap(path)
        if not pixmap.isNull():
            pixmap = pixmap.scaled(
                BOOKSHELF_CONFIG['cover_width'], 
                BOOKSHELF_CONFIG['cover_height'],
                Qt.AspectRatioMode.KeepAspectRatio, 
                Qt.TransformationMode.SmoothTransformation
            )
            self.cover_label.setPixmap(pixmap)
        else:
            self._set_default_cover()
    
    def _set_default_cover(self):
        """设置默认封面"""
        pixmap = QPixmap(
            BOOKSHELF_CONFIG['cover_width'], 
            BOOKSHELF_CONFIG['cover_height']
        )
        pixmap.fill(QColor(100, 100, 100))
        
        painter = QPainter(pixmap)
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(painter.font())
        painter.drawText(
            pixmap.rect(), 
            Qt.AlignmentFlag.AlignCenter, 
            self.book.display_title
        )
        painter.end()
        
        self.cover_label.setPixmap(pixmap)
    
    def _on_delete_clicked(self):
        """删除按钮点击事件"""
        self.delete_clicked.emit(self.book)

    def set_delete_mode(self, enabled: bool):
        """设置删除模式"""
        self.show_delete_button = enabled
        if hasattr(self, 'delete_button'):
            self.delete_button.setVisible(enabled)
        elif enabled:
            # 如果之前没有删除按钮，现在创建一个
            self.delete_button = QPushButton("×")
            # 设置为正方形按钮，确保完全显示
            button_size = 24
            self.delete_button.setFixedSize(button_size, button_size)
            self.delete_button.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-size: 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            self.delete_button.clicked.connect(self._on_delete_clicked)
            self.delete_button.setParent(self)
            self.delete_button.move(
                BOOKSHELF_CONFIG['book_width'] - button_size - 2, 2
            )
            self.delete_button.show()

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.book_clicked.emit(self.book)
        super().mousePressEvent(event)

    def refresh_styles(self):
        """刷新样式"""
        from config import styles

        # 刷新封面样式
        self.cover_label.setStyleSheet(styles.get_book_cover_style())

        # 刷新标题样式
        self.title_label.setStyleSheet(styles.get_book_title_style())

        # 刷新作者样式
        self.author_label.setStyleSheet(styles.get_book_author_style())
