"""阅读窗口"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTextEdit, QPushButton, QSlider, QLabel, QFrame,
                               QGraphicsOpacityEffect, QApplication)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, Signal, QRect
from PySide6.QtGui import QFont, QKeyEvent, QPalette
import os
from typing import Optional

from core.models.ebook import EBook
from config.settings import COLORS


class ReadingDrawer(QFrame):
    """阅读设置抽屉"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("reading_drawer")
        self.setStyleSheet(f"""
            QFrame#reading_drawer {{
                background-color: {COLORS['secondary_bg']};
                border-top: 2px solid {COLORS['accent_color']};
                border-radius: 10px 10px 0 0;
            }}
            QLabel {{
                color: {COLORS['text_primary']};
                font-size: 14px;
                font-weight: bold;
            }}
            QSlider::groove:horizontal {{
                border: 1px solid {COLORS['accent_bg']};
                height: 8px;
                background: {COLORS['accent_bg']};
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {COLORS['accent_color']};
                border: 1px solid {COLORS['accent_color']};
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }}
            QSlider::handle:horizontal:hover {{
                background: {COLORS['button_hover']};
            }}
        """)
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 字体大小调节
        font_layout = QHBoxLayout()
        font_label = QLabel("字体大小:")
        font_label.setFixedWidth(80)
        self.font_slider = QSlider(Qt.Orientation.Horizontal)
        self.font_slider.setMinimum(12)
        self.font_slider.setMaximum(24)
        self.font_slider.setValue(16)
        self.font_slider.setFixedWidth(150)  # 设置固定宽度
        self.font_value_label = QLabel("16")
        self.font_value_label.setFixedWidth(30)

        font_layout.addWidget(font_label)
        font_layout.addWidget(self.font_slider)
        font_layout.addWidget(self.font_value_label)
        font_layout.addStretch()  # 添加弹性空间

        # 字体粗细调节
        weight_layout = QHBoxLayout()
        weight_label = QLabel("字体粗细:")
        weight_label.setFixedWidth(80)
        self.weight_slider = QSlider(Qt.Orientation.Horizontal)
        self.weight_slider.setMinimum(0)   # 对应 QFont.Weight.Thin
        self.weight_slider.setMaximum(8)   # 对应 QFont.Weight.Black
        self.weight_slider.setValue(3)     # 对应 QFont.Weight.Normal
        self.weight_slider.setFixedWidth(150)  # 设置固定宽度
        self.weight_value_label = QLabel("正常")
        self.weight_value_label.setFixedWidth(30)

        weight_layout.addWidget(weight_label)
        weight_layout.addWidget(self.weight_slider)
        weight_layout.addWidget(self.weight_value_label)
        weight_layout.addStretch()  # 添加弹性空间

        # 行间距调节
        line_layout = QHBoxLayout()
        line_label = QLabel("行间距:")
        line_label.setFixedWidth(80)
        self.line_slider = QSlider(Qt.Orientation.Horizontal)
        self.line_slider.setMinimum(100)
        self.line_slider.setMaximum(200)
        self.line_slider.setValue(120)
        self.line_slider.setFixedWidth(150)  # 设置固定宽度
        self.line_value_label = QLabel("1.2")
        self.line_value_label.setFixedWidth(30)

        line_layout.addWidget(line_label)
        line_layout.addWidget(self.line_slider)
        line_layout.addWidget(self.line_value_label)
        line_layout.addStretch()  # 添加弹性空间

        layout.addLayout(font_layout)
        layout.addLayout(weight_layout)
        layout.addLayout(line_layout)

        # 连接信号
        self.font_slider.valueChanged.connect(self._on_font_size_changed)
        self.weight_slider.valueChanged.connect(self._on_font_weight_changed)
        self.line_slider.valueChanged.connect(self._on_line_spacing_changed)
    
    def _on_font_size_changed(self, value):
        """字体大小改变"""
        self.font_value_label.setText(str(value))

    def _on_font_weight_changed(self, value):
        """字体粗细改变"""
        weight_names = {
            0: "极细", 1: "超细", 2: "细", 3: "正常",
            4: "中等", 5: "半粗", 6: "粗", 7: "超粗", 8: "极粗"
        }
        self.weight_value_label.setText(weight_names.get(value, "正常"))

    def _on_line_spacing_changed(self, value):
        """行间距改变"""
        spacing = value / 100.0
        self.line_value_label.setText(f"{spacing:.1f}")


class ReadingWindow(QMainWindow):
    """阅读窗口"""
    
    # 信号：关闭阅读窗口
    reading_closed = Signal()
    
    def __init__(self, book: EBook, parent=None):
        super().__init__(parent)
        self.book = book
        self.current_page = 0
        self.pages = []
        self.chars_per_page = 1000  # 每页字符数
        self.drawer_visible = False
        self.hide_timer = QTimer()
        self.hide_timer.setSingleShot(True)
        self.hide_timer.timeout.connect(self._hide_drawer)
        
        self._setup_ui()
        self._load_book_content()
        self._setup_window()
        self._connect_signals()
    
    def _setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle(f"阅读 - {self.book.title}")
        self.setMinimumSize(800, 600)
        # 设置合适的窗口大小（不全屏）
        self.resize(1000, 700)

        # 设置焦点策略，确保能接收键盘事件
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
    
    def _setup_ui(self):
        """设置UI"""
        # 主容器
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(self.central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 顶部工具栏
        self.toolbar = self._create_toolbar()

        # 阅读区域容器
        self.reading_container = QWidget()
        reading_layout = QHBoxLayout(self.reading_container)
        reading_layout.setContentsMargins(20, 20, 20, 20)
        reading_layout.setSpacing(20)
        
        # 左侧翻页按钮
        self.prev_button = QPushButton("◀")
        self.prev_button.setFixedSize(50, 100)
        self.prev_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['secondary_bg']};
                color: {COLORS['text_primary']};
                border: 2px solid {COLORS['accent_color']};
                border-radius: 25px;
                font-size: 20px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {COLORS['accent_color']};
            }}
            QPushButton:pressed {{
                background-color: {COLORS['button_hover']};
            }}
        """)
        
        # 阅读文本区域
        self.text_area = QTextEdit()
        self.text_area.setReadOnly(True)
        self.text_area.setFocusPolicy(Qt.FocusPolicy.NoFocus)  # 不接收焦点，避免拦截键盘事件
        self.text_area.setStyleSheet(f"""
            QTextEdit {{
                background-color: {COLORS['primary_bg']};
                color: {COLORS['text_primary']};
                border: none;
                padding: 40px;
                font-family: "Microsoft YaHei", "SimSun", serif;
                font-size: 16px;
                line-height: 1.6;
            }}
        """)
        
        # 右侧翻页按钮
        self.next_button = QPushButton("▶")
        self.next_button.setFixedSize(50, 100)
        self.next_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['secondary_bg']};
                color: {COLORS['text_primary']};
                border: 2px solid {COLORS['accent_color']};
                border-radius: 25px;
                font-size: 20px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {COLORS['accent_color']};
            }}
            QPushButton:pressed {{
                background-color: {COLORS['button_hover']};
            }}
        """)
        
        reading_layout.addWidget(self.prev_button)
        reading_layout.addWidget(self.text_area, 1)
        reading_layout.addWidget(self.next_button)
        
        # 抽屉容器
        self.drawer = ReadingDrawer()
        self.drawer.setFixedHeight(0)  # 初始隐藏
        
        main_layout.addWidget(self.toolbar)
        main_layout.addWidget(self.reading_container, 1)
        main_layout.addWidget(self.drawer)
        
        # 设置窗口背景
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {COLORS['primary_bg']};
            }}
        """)

    def _create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QWidget()
        toolbar.setFixedHeight(50)
        toolbar.setStyleSheet(f"""
            QWidget {{
                background-color: {COLORS['secondary_bg']};
                border-bottom: 1px solid {COLORS['accent_bg']};
            }}
        """)

        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)

        # 书籍标题
        self.book_title_label = QLabel(self.book.title)
        self.book_title_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['text_primary']};
                font-size: 16px;
                font-weight: bold;
            }}
        """)

        # 关闭按钮
        self.close_button = QPushButton("✕ 关闭")
        self.close_button.setFixedSize(80, 30)
        self.close_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['accent_bg']};
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['accent_color']};
                border-radius: 4px;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {COLORS['accent_color']};
            }}
            QPushButton:pressed {{
                background-color: {COLORS['button_hover']};
            }}
        """)

        toolbar_layout.addWidget(self.book_title_label)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.close_button)

        return toolbar

    def _connect_signals(self):
        """连接信号"""
        # 翻页按钮
        self.prev_button.clicked.connect(self._prev_page)
        self.next_button.clicked.connect(self._next_page)

        # 关闭按钮
        self.close_button.clicked.connect(self.close)

        # 文本区域点击事件
        self.text_area.mousePressEvent = self._on_text_area_clicked

        # 抽屉设置变化
        self.drawer.font_slider.valueChanged.connect(self._apply_font_size_change)
        self.drawer.weight_slider.valueChanged.connect(self._apply_font_weight_change)
        self.drawer.line_slider.valueChanged.connect(self._apply_line_spacing_change)

    def _load_book_content(self):
        """加载书籍内容"""
        try:
            if not self.book.file_path or not os.path.exists(self.book.file_path):
                self.text_area.setText("错误：无法找到书籍文件")
                return

            content = self._read_file_with_encoding_detection(self.book.file_path)
            if content is None:
                self.text_area.setText("错误：无法读取书籍文件，不支持的编码格式")
                return

            # 分页处理
            self._split_content_to_pages(content)
            self._display_current_page()

        except Exception as e:
            self.text_area.setText(f"错误：无法读取书籍文件\n{str(e)}")

    def _read_file_with_encoding_detection(self, file_path: str) -> str:
        """使用多种编码尝试读取文件"""
        # 常见的编码格式列表，按优先级排序
        encodings = [
            'utf-8',           # UTF-8 (最常用)
            'gbk',             # 中文 GBK
            'gb2312',          # 中文 GB2312
            'gb18030',         # 中文 GB18030
            'big5',            # 繁体中文 Big5
            'utf-16',          # UTF-16
            'utf-16le',        # UTF-16 Little Endian
            'utf-16be',        # UTF-16 Big Endian
            'ascii',           # ASCII
            'latin1',          # Latin-1 (ISO-8859-1)
            'cp1252',          # Windows-1252
            'iso-8859-1',      # ISO-8859-1
        ]

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    content = file.read()
                    print(f"成功使用 {encoding} 编码读取文件: {file_path}")
                    return content
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取文件时出错: {e}")
                continue

        # 如果所有编码都失败，尝试使用chardet库进行自动检测
        try:
            import chardet
            with open(file_path, 'rb') as file:
                raw_data = file.read()
                result = chardet.detect(raw_data)
                detected_encoding = result['encoding']
                confidence = result['confidence']

                if detected_encoding and confidence > 0.7:  # 置信度大于70%
                    try:
                        content = raw_data.decode(detected_encoding)
                        print(f"使用chardet检测到的编码 {detected_encoding} (置信度: {confidence:.2f}) 成功读取文件")
                        return content
                    except Exception as e:
                        print(f"使用检测到的编码 {detected_encoding} 读取失败: {e}")
        except ImportError:
            print("chardet库未安装，无法进行自动编码检测")
        except Exception as e:
            print(f"chardet编码检测失败: {e}")

        print(f"所有编码尝试都失败，无法读取文件: {file_path}")
        return None

    def _split_content_to_pages(self, content: str):
        """将内容分页"""
        self.pages = []

        # 改进的分页逻辑：按字符数分页，但在合适的位置断页
        start = 0
        while start < len(content):
            end = start + self.chars_per_page

            if end >= len(content):
                # 最后一页
                self.pages.append(content[start:])
                break

            # 查找合适的断页位置
            page_content = content[start:end]

            # 查找最后一个句号、感叹号、问号或段落结束
            break_points = [
                page_content.rfind('。'),
                page_content.rfind('！'),
                page_content.rfind('？'),
                page_content.rfind('\n\n'),
                page_content.rfind('。\n'),
                page_content.rfind('！\n'),
                page_content.rfind('？\n')
            ]

            best_break = max(break_points)

            # 如果找到合适的断点且不太靠前
            if best_break > self.chars_per_page * 0.6:
                actual_end = start + best_break + 1
                self.pages.append(content[start:actual_end])
                start = actual_end
            else:
                # 没找到合适断点，直接按字符数分页
                self.pages.append(content[start:end])
                start = end

    def _display_current_page(self):
        """显示当前页"""
        if not self.pages:
            return

        if 0 <= self.current_page < len(self.pages):
            self.text_area.setText(self.pages[self.current_page])

            # 更新窗口标题显示页码
            self.setWindowTitle(f"阅读 - {self.book.title} ({self.current_page + 1}/{len(self.pages)})")

            # 更新按钮状态
            self.prev_button.setEnabled(self.current_page > 0)
            self.next_button.setEnabled(self.current_page < len(self.pages) - 1)

    def _prev_page(self):
        """上一页"""
        if self.current_page > 0:
            self.current_page -= 1
            self._display_current_page()

    def _next_page(self):
        """下一页"""
        if self.current_page < len(self.pages) - 1:
            self.current_page += 1
            self._display_current_page()

    def _on_text_area_clicked(self, event):
        """文本区域点击事件"""
        # 调用原始的鼠标按下事件
        QTextEdit.mousePressEvent(self.text_area, event)

        # 切换抽屉显示状态
        if self.drawer_visible:
            self._hide_drawer()
        else:
            self._show_drawer()

    def _show_drawer(self):
        """显示抽屉"""
        if self.drawer_visible:
            return

        self.drawer_visible = True

        # 计算抽屉高度（屏幕高度的35%）
        screen_height = self.height()
        drawer_height = int(screen_height * 0.35)

        # 创建动画
        self.drawer_animation = QPropertyAnimation(self.drawer, b"maximumHeight")
        self.drawer_animation.setDuration(300)
        self.drawer_animation.setStartValue(0)
        self.drawer_animation.setEndValue(drawer_height)
        self.drawer_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        # 同时设置最小高度
        self.drawer_min_animation = QPropertyAnimation(self.drawer, b"minimumHeight")
        self.drawer_min_animation.setDuration(300)
        self.drawer_min_animation.setStartValue(0)
        self.drawer_min_animation.setEndValue(drawer_height)
        self.drawer_min_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        self.drawer_animation.start()
        self.drawer_min_animation.start()

        # 3秒后自动隐藏
        self.hide_timer.start(3000)

    def _hide_drawer(self):
        """隐藏抽屉"""
        if not self.drawer_visible:
            return

        self.drawer_visible = False
        self.hide_timer.stop()

        # 创建动画
        self.drawer_animation = QPropertyAnimation(self.drawer, b"maximumHeight")
        self.drawer_animation.setDuration(300)
        self.drawer_animation.setStartValue(self.drawer.height())
        self.drawer_animation.setEndValue(0)
        self.drawer_animation.setEasingCurve(QEasingCurve.Type.InCubic)

        # 同时设置最小高度
        self.drawer_min_animation = QPropertyAnimation(self.drawer, b"minimumHeight")
        self.drawer_min_animation.setDuration(300)
        self.drawer_min_animation.setStartValue(self.drawer.height())
        self.drawer_min_animation.setEndValue(0)
        self.drawer_min_animation.setEasingCurve(QEasingCurve.Type.InCubic)

        self.drawer_animation.start()
        self.drawer_min_animation.start()

    def _apply_font_size_change(self, size):
        """应用字体大小改变"""
        # 更新抽屉中的显示
        self.drawer.font_value_label.setText(str(size))

        # 应用到文本区域
        font = self.text_area.font()
        font.setPointSize(size)
        self.text_area.setFont(font)

        # 重新启动隐藏计时器
        if self.drawer_visible:
            self.hide_timer.start(3000)

    def _apply_font_weight_change(self, weight_index):
        """应用字体粗细改变"""
        # 定义权重映射
        weight_names = {
            0: "极细", 1: "超细", 2: "细", 3: "正常",
            4: "中等", 5: "半粗", 6: "粗", 7: "超粗", 8: "极粗"
        }

        # QFont.Weight 枚举值映射
        weight_values = {
            0: QFont.Weight.Thin,      # 100
            1: QFont.Weight.ExtraLight, # 200
            2: QFont.Weight.Light,     # 300
            3: QFont.Weight.Normal,    # 400
            4: QFont.Weight.Medium,    # 500
            5: QFont.Weight.DemiBold,  # 600
            6: QFont.Weight.Bold,      # 700
            7: QFont.Weight.ExtraBold, # 800
            8: QFont.Weight.Black      # 900
        }

        # 更新抽屉中的显示
        self.drawer.weight_value_label.setText(weight_names.get(weight_index, "正常"))

        # 应用到文本区域
        font = self.text_area.font()
        font.setWeight(weight_values.get(weight_index, QFont.Weight.Normal))
        self.text_area.setFont(font)

        # 重新启动隐藏计时器
        if self.drawer_visible:
            self.hide_timer.start(3000)

    def _apply_line_spacing_change(self, value):
        """应用行间距改变"""
        spacing = value / 100.0

        # 更新抽屉中的显示
        self.drawer.line_value_label.setText(f"{spacing:.1f}")

        # 设置行间距
        cursor = self.text_area.textCursor()
        block_format = cursor.blockFormat()
        block_format.setLineHeight(spacing * 100, 1)  # 1 表示百分比模式

        # 应用到整个文档
        cursor.select(cursor.SelectionType.Document)
        cursor.setBlockFormat(block_format)

        # 重新启动隐藏计时器
        if self.drawer_visible:
            self.hide_timer.start(3000)

    def keyPressEvent(self, event: QKeyEvent):
        """键盘事件处理"""
        key = event.key()

        if key == Qt.Key.Key_Left or key == Qt.Key.Key_A:
            self._prev_page()
            event.accept()
        elif key == Qt.Key.Key_Right or key == Qt.Key.Key_D:
            self._next_page()
            event.accept()
        elif key == Qt.Key.Key_Escape:
            self.close()
            event.accept()
        else:
            super().keyPressEvent(event)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 发送关闭信号
        self.reading_closed.emit()
        event.accept()
