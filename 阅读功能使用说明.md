# 阅读功能使用说明

## 功能概述

现在已经成功实现了阅读书籍的功能！用户可以点击书架上的任意书籍，打开专门的阅读界面进行阅读。

## 主要功能

### 1. 打开阅读界面
- 在书架页面点击任意书籍
- 主窗口会自动隐藏
- 阅读窗口会全屏显示

### 2. 阅读区域
- **中间区域**：显示txt文件的内容，支持分页显示
- **智能分页**：自动在句号、感叹号、问号或段落结束处分页，避免句子被截断
- **页码显示**：窗口标题显示当前页码和总页数

### 3. 翻页功能
- **左右按钮**：点击阅读区域两侧的 ◀ 和 ▶ 按钮进行翻页
- **键盘控制**：
  - 左方向键：上一页
  - 右方向键：下一页
  - ESC键：关闭阅读窗口

### 4. 阅读设置抽屉
- **触发方式**：点击阅读区域（文本部分）
- **抽屉位置**：从窗口底部弹出，占屏幕高度的35%
- **自动隐藏**：3秒后自动隐藏
- **设置选项**：
  - **字体大小调节**：12-24号字体，使用滑块调节
  - **行间距调整**：1.0-2.0倍行距，使用滑块调节

### 5. 关闭阅读窗口
- **关闭按钮**：点击顶部工具栏的"✕ 关闭"按钮
- **键盘快捷键**：按ESC键
- **自动返回**：关闭阅读窗口后自动显示主界面

## 界面布局

```
┌─────────────────────────────────────────────────────────┐
│ [书籍标题]                                    [✕ 关闭] │ ← 顶部工具栏
├─────────────────────────────────────────────────────────┤
│ ◀  │                                          │  ▶     │
│    │                                          │        │
│    │            阅读内容区域                    │        │ ← 主阅读区域
│    │         (点击弹出设置抽屉)                  │        │
│    │                                          │        │
├─────────────────────────────────────────────────────────┤
│                   设置抽屉                              │ ← 点击阅读区域后弹出
│ 字体大小: [────●────] 16                               │   (3秒后自动隐藏)
│ 行间距:   [──●──────] 1.2                             │
└─────────────────────────────────────────────────────────┘
```

## 技术特性

- **响应式设计**：支持全屏显示，适配不同屏幕尺寸
- **流畅动画**：抽屉弹出/隐藏使用平滑动画效果
- **智能分页**：避免在句子中间断页，提供更好的阅读体验
- **实时设置**：字体和行距调整立即生效
- **键盘支持**：支持键盘快捷键操作

## 使用建议

1. **首次使用**：建议先调整字体大小和行间距到舒适的设置
2. **翻页操作**：推荐使用键盘左右键进行翻页，操作更流畅
3. **设置调整**：点击阅读区域即可快速调整设置，无需额外菜单
4. **长时间阅读**：可以使用全屏模式，减少干扰

## 注意事项

- 目前仅支持UTF-8编码的txt文件
- 分页是基于字符数的，不同字体大小可能影响实际显示效果
- 设置抽屉会在3秒后自动隐藏，如需继续调整请重新点击阅读区域
